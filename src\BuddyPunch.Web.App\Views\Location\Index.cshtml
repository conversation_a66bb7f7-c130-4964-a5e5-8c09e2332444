﻿@using BuddyPunch.Web.App.Helpers
@using BuddyPunch.Web.App.ViewModel
@model BuddyPunch.Web.App.ViewModel.LocationListViewModel
@{
    ViewBag.Title = "Locations";
}

@Html.Partial("_ErrorSuccessPartial")


<!-- BEGIN PAGE CONTENT-->

<div class="kt-card rounded-xl mb-7.5">
    <div class="flex items-center flex-wrap md:flex-nowrap justify-between grow gap-5 p-5 rtl:bg-[center_left_-8rem] bg-[center_right_-8rem] bg-no-repeat bg-[length:700px] upgrade-bg">
        <div class="flex items-center gap-4">
            <div class="relative size-[50px] shrink-0">
                <svg class="w-full h-full stroke-primary/10 fill-primary-soft" fill="none" height="48" viewbox="0 0 44 48" width="44" xmlns="http://www.w3.org/2000/svg">
                    <path d="M16 2.4641C19.7128 0.320509 24.2872 0.320508 28 2.4641L37.6506 8.0359C41.3634 10.1795 43.6506 14.141 43.6506
			18.4282V29.5718C43.6506 33.859 41.3634 37.8205 37.6506 39.9641L28 45.5359C24.2872 47.6795 19.7128 47.6795 16 45.5359L6.34937
			39.9641C2.63655 37.8205 0.349365 33.859 0.349365 29.5718V18.4282C0.349365 14.141 2.63655 10.1795 6.34937 8.0359L16 2.4641Z" fill="">
                    </path>
                    <path d="M16.25 2.89711C19.8081 0.842838 24.1919 0.842837 27.75 2.89711L37.4006 8.46891C40.9587 10.5232 43.1506 14.3196 43.1506
			18.4282V29.5718C43.1506 33.6804 40.9587 37.4768 37.4006 39.5311L27.75 45.1029C24.1919 47.1572 19.8081 47.1572 16.25 45.1029L6.59937
			39.5311C3.04125 37.4768 0.849365 33.6803 0.849365 29.5718V18.4282C0.849365 14.3196 3.04125 10.5232 6.59937 8.46891L16.25 2.89711Z" stroke="">
                    </path>
                </svg>
                <div class="absolute leading-none start-2/4 top-2/4 -translate-y-2/4 -translate-x-2/4 rtl:translate-x-2/4">
                    <i class="ki-filled ki-cheque text-xl text-primary">
                    </i>
                </div>
            </div>
            <div class="flex flex-col gap-2">

                <div class="text-sm text-secondary-foreground">
                    Locations are created here and then assigned to employees in Employees -> View.<br />
                    When employees go to punch in they can select a location to punch into.<br />
                    You can also associate multiple department codes to Locations, in case you need that level of complexity.<br />
                    Each employee can have as many locations as required.
                </div>
            </div>
        </div>
        <div class="flex items-center gap-1.5 shrink-0">

            <a class="kt-btn kt-btn-mono" href="@Url.Action("Create")">
                Add a New Location
            </a>
        </div>
    </div>
</div>

<!-- Container -->

<div class="kt-card kt-card-grid min-w-full">
    <div class="kt-card-header flex-wrap gap-2">
        <div class="kt-tabs kt-tabs-line border-0" data-kt-tabs="true">
            <button class="kt-tab-toggle active selected" data-kt-tab-toggle="#tab_Active">
                Active (@Model.Locations.Where(l => l.Enabled).Count())
            </button><button class="kt-tab-toggle" data-kt-tab-toggle="#tab_InActive">
                Inactive (@Model.Locations.Where(l => !l.Enabled).Count())
            </button><button class="kt-tab-toggle" data-kt-tab-toggle="#tab_All">
                All (@Model.Locations.Count())
            </button>
        </div>

        <div class="flex flex-wrap gap-2 lg:gap-5">
            <div class="flex">
                <label class="kt-input">
                    <i class="ki-filled ki-magnifier">
                    </i>
                    <input id="location-search" placeholder="Search locations..." type="text" value="" />
                </label>
            </div>
            <div class="flex flex-wrap gap-2.5">
                <button class="kt-btn kt-btn-success activate-location" id="activate-btn" style="display: none;">
                    <i class="ki-duotone ki-check"></i>
                    Activate
                </button>
                <button class="kt-btn kt-btn-danger deactivate-location" id="deactivate-btn" style="display: none;">
                    <i class="ki-duotone ki-cross"></i>
                    Deactivate
                </button>
            </div>
        </div>
    </div>
    <div class="kt-card-content">
        <div class="text-sm">

            <div class="" id="tab_Active">
                <div class="grid" data-kt-datatable="true" data-kt-datatable-page-size="10" id="active_datatable">
                    @RenderLocations(Model.Locations.Where(l => l.Enabled).OrderBy(e => e.Name), "active_table")
                </div>
            </div>
            <div class="hidden" id="tab_InActive">
                <div class="grid" data-kt-datatable="true" data-kt-datatable-page-size="10" id="inactive_datatable">
                    @RenderLocations(Model.Locations.Where(l => !l.Enabled).OrderBy(e => e.Name), "inactive_table")
                </div>
            </div>
            <div class="hidden" id="tab_All">
                <div class="grid" data-kt-datatable="true" data-kt-datatable-page-size="10" id="all_datatable">
                    @RenderLocations(Model.Locations.OrderBy(e => e.Name), "all_table")
                </div>
            </div>
        </div>
    </div>
</div>


<!-- End of Container -->



@helper RenderLocations(IEnumerable<LocationViewModel> locations, string id)
{
    <div class="kt-scrollable-x-auto">
        <table class="kt-table table-auto kt-table-border" data-kt-datatable-table="true" id="@id">
            <thead>
                <tr>
                    <th class="w-[60px] text-center">
                        <input class="kt-checkbox kt-checkbox-sm" data-kt-datatable-check="true" type="checkbox" />
                    </th>
                    <th class="min-w-[200px]">
                        <span class="kt-table-col asc">
                            <span class="kt-table-col-label">
                                Name
                            </span>
                            <span class="kt-table-col-sort">
                            </span>
                        </span>
                    </th>
                    <th class="min-w-[250px]">
                        <span class="kt-table-col">
                            <span class="kt-table-col-label">
                                Employees Assigned
                            </span>
                            <span class="kt-table-col-sort">
                            </span>
                        </span>
                    </th>
                    <th class="min-w-[190px]">
                        <span class="kt-table-col">
                            <span class="kt-table-col-label">
                                Active
                            </span>
                            <span class="kt-table-col-sort">
                            </span>
                        </span>
                    </th>
                    <th class="min-w-[190px]">
                        <span class="kt-table-col">
                            <span class="kt-table-col-label">
                                Billable
                            </span>
                            <span class="kt-table-col-sort">
                            </span>
                        </span>
                    </th>
                    <th class="w-[60px]">
                    </th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in locations)
                {
                    <tr>
                        <td class="text-center">
                            <input class="kt-checkbox kt-checkbox-sm location-checkbox" data-kt-datatable-row-check="true" type="checkbox" value="1" data-location-id="@item.Id" data-location-enabled="@item.Enabled.ToString().ToLower()" />
                            <span class="hidden" data-id="@item.Id"></span>
                        </td>
                        <td>
                            <div class="flex items-center gap-2.5">
                                <a class="text-sm font-medium text-mono hover:text-primary" href="@Url.Action("Edit", "Location", new { id = item.Id })">
                                    @item.Name
                                </a>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center gap-1.5 text-foreground font-normal">
                                @item.EmployeesAssigned
                            </div>
                        </td>
                        <td class="text-foreground font-normal">
                            @(item.Enabled ? "Yes" : "No")
                        </td>
                        <td>
                            <div class="flex items-center gap-1.5 text-foreground font-normal">
                                @(item.Billable ? "Yes" : "No")
                            </div>
                        </td>
                        <td>
                            <div class="kt-menu" data-kt-menu="true">
                                <div class="kt-menu-item" data-kt-menu-item-offset="0, 10px" data-kt-menu-item-placement="bottom-end" data-kt-menu-item-placement-rtl="bottom-start" data-kt-menu-item-toggle="dropdown" data-kt-menu-item-trigger="click">
                                    <button class="kt-menu-toggle kt-btn kt-btn-sm kt-btn-icon kt-btn-ghost">
                                        <i class="ki-filled ki-dots-vertical text-lg">
                                        </i>
                                    </button>
                                    <div class="kt-menu-dropdown kt-menu-default w-full max-w-[175px]" data-kt-menu-dismiss="true">
                                        <div class="kt-menu-separator">
                                        </div>
                                        <div class="kt-menu-item">
                                            <a class="kt-menu-link" href="@Url.Action("Edit", "Location", new { id = item.Id })">
                                                <span class="kt-menu-icon">
                                                    <i class="ki-filled ki-pencil">
                                                    </i>
                                                </span>
                                                <span class="kt-menu-title">
                                                    Edit
                                                </span>
                                            </a>
                                        </div>
                                        <div class="kt-menu-item">
                                            <a class="kt-menu-link" href="@Url.Action("Clone", "Location", new { id = item.Id })">
                                                <span class="kt-menu-icon">
                                                    <i class="ki-filled ki-copy">
                                                    </i>
                                                </span>
                                                <span class="kt-menu-title">
                                                    Make a copy
                                                </span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
        <div class="kt-card-footer justify-center md:justify-between flex-col md:flex-row gap-5 text-secondary-foreground text-sm font-medium">
            <div class="flex items-center gap-2 order-2 md:order-1">
                Show
                <select class="kt-select w-16" data-kt-datatable-size="true" data-kt-select="" name="perpage">
                </select>
                per page
            </div>
            <div class="flex items-center gap-4 order-1 md:order-2">
                <span data-kt-datatable-info="true">
                </span>
                <div class="kt-datatable-pagination" data-kt-datatable-pagination="true">
                </div>
            </div>
        </div>
    </div>

}
@section PageBeforeEndBodySection
{
    <script type="text/javascript">


        // Function to update button visibility based on selected checkboxes
        function updateButtonVisibility() {
            var selectedActive = [];
            var selectedInactive = [];

            $('.location-checkbox:checked').each(function() {
                var isEnabled = $(this).data('location-enabled');
                var locationId = $(this).data('location-id');

                if (isEnabled === true || isEnabled === 'true') {
                    selectedActive.push(locationId);
                } else {
                    selectedInactive.push(locationId);
                }
            });

            // Show/hide buttons based on selection
            if (selectedActive.length > 0 && selectedInactive.length === 0) {
                // Only active locations selected - show deactivate button
                $('#activate-btn').hide();
                $('#deactivate-btn').show();
            } else if (selectedInactive.length > 0 && selectedActive.length === 0) {
                // Only inactive locations selected - show activate button
                $('#activate-btn').show();
                $('#deactivate-btn').hide();
            } else if (selectedActive.length === 0 && selectedInactive.length === 0) {
                // No locations selected - hide both buttons
                $('#activate-btn').hide();
                $('#deactivate-btn').hide();
            } else {
                // Mixed selection - hide both buttons
                $('#activate-btn').hide();
                $('#deactivate-btn').hide();
            }
        }

        jQuery(document).ready(function () {

        var selectedArray = [];
        var selectedTab = 1;

        // Initialize Metronic DataTables
        var datatables = {};
        if (typeof KTDatatable !== 'undefined') {
            // Initialize each datatable separately and store references
            $('#active_datatable').each(function() {
                if ($(this).find('[data-kt-datatable-table]').length > 0) {
                    datatables.active = new KTDatatable(this);
                }
            });

            $('#inactive_datatable').each(function() {
                if ($(this).find('[data-kt-datatable-table]').length > 0) {
                    datatables.inactive = new KTDatatable(this);
                }
            });

            $('#all_datatable').each(function() {
                if ($(this).find('[data-kt-datatable-table]').length > 0) {
                    datatables.all = new KTDatatable(this);
                }
            });
        }

        // Initialize search functionality
        initializeSearch();

        // Handle tab switching
        $(document).on('click', '.kt-tab-toggle', function() {
            var target = $(this).data('kt-tab-toggle');
            if (target) {
                // Reset selections when switching tabs
                resetLocationSelect(selectedTab);

                // Update selectedTab based on target
                if (target.includes('Active')) {
                    selectedTab = 1;
                } else if (target.includes('InActive')) {
                    selectedTab = 2;
                } else if (target.includes('All')) {
                    selectedTab = 3;
                }

                // Re-apply search if there's a search term
                setTimeout(function() {
                    var searchTerm = $('#location-search').val();
                    if (searchTerm && searchTerm.trim() !== '') {
                        performSearch(searchTerm.toLowerCase().trim());
                    }
                }, 100);
            }
        });

        // Handle individual row checkboxes
        $(document).on('change', '[data-kt-datatable-row-check="true"]', function () {
            var checked = $(this).prop('checked');
            var id = $(this).closest('tr').find('[data-id]').data('id');

            if (checked) {
                if (!selectedArray.includes(id)) {
                    selectedArray.push(id);
                }
            } else {
                selectedArray = selectedArray.filter(empId => empId !== id);
            }

            updateActionButtonsVisibility();
            updateHeaderCheckbox();
        });

        // Handle header "select all" checkboxes
        $(document).on('change', '[data-kt-datatable-check="true"]', function () {
            var isChecked = $(this).prop('checked');
            var currentTable = $(this).closest('[data-kt-datatable="true"]');
            var checkboxes = currentTable.find('[data-kt-datatable-row-check="true"]');

            checkboxes.prop('checked', isChecked);

            selectedArray = isChecked
                ? checkboxes.map(function () {
                    return $(this).closest('tr').find('[data-id]').data('id');
                }).get().filter(id => id !== undefined)
                : [];

            updateActionButtonsVisibility();
        });

        function updateHeaderCheckbox() {
            var currentTab = $('.tab-content .active, #tab_Active:not(.hidden), #tab_InActive:not(.hidden), #tab_All:not(.hidden)').first();
            var totalCheckboxes = currentTab.find('[data-kt-datatable-row-check="true"]').length;
            var checkedCheckboxes = currentTab.find('[data-kt-datatable-row-check="true"]:checked').length;
            var headerCheckbox = currentTab.find('[data-kt-datatable-check="true"]');

            if (checkedCheckboxes === 0) {
                headerCheckbox.prop('checked', false).prop('indeterminate', false);
            } else if (checkedCheckboxes === totalCheckboxes) {
                headerCheckbox.prop('checked', true).prop('indeterminate', false);
            } else {
                headerCheckbox.prop('checked', false).prop('indeterminate', true);
            }
        }

        $(document).on('click', '.activate-location', function (e) {
            e.preventDefault();

            if (selectedArray.length === 0) {
                return;
            }
            $.post("@Url.Action("ActivateAll", "Location")", {
                ids: ArrayToString(selectedArray),
                activate: true
            }, function () {
                window.location.href = '@Url.Action("Index", "Location")';
            });

            resetLocationSelect(selectedTab);
        });

        $(document).on('click', '.deactivate-location', function (e) {
            e.preventDefault();

            if (selectedArray.length === 0) {
                return;
            }
            $.post("@Url.Action("ActivateAll", "Location")", {
                ids: ArrayToString(selectedArray),
                activate: false
            }, function () {
                window.location.href = '@Url.Action("Index", "Location")';
            });

            resetLocationSelect(selectedTab);
        });

        function updateActionButtonsVisibility() {
            // Call the new function that checks actual enabled status
            updateButtonVisibility();
        }

        function resetLocationSelect(tab) {
            selectedTab = tab;
            selectedArray = [];
            $('[data-kt-datatable-row-check="true"]').each(function () {
                $(this).prop('checked', false);
            });
            $('[data-kt-datatable-check="true"]').each(function () {
                $(this).prop('checked', false).prop('indeterminate', false);
            });
            // Hide both buttons when resetting
            $('#activate-btn').hide();
            $('#deactivate-btn').hide();
        }

        // Initialize search functionality
        function initializeSearch() {
            var searchInput = $('#location-search');
            var searchTimeout;

            // Handle search input with debouncing
            searchInput.on('input', function() {
                var searchTerm = $(this).val().toLowerCase().trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debouncing (300ms delay)
                searchTimeout = setTimeout(function() {
                    performSearch(searchTerm);
                }, 300);
            });

            // Handle Enter key
            searchInput.on('keypress', function(e) {
                if (e.which === 13) { // Enter key
                    e.preventDefault();
                    clearTimeout(searchTimeout);
                    var searchTerm = $(this).val().toLowerCase().trim();
                    performSearch(searchTerm);
                }
            });

            // Handle clear search (when input is emptied)
            searchInput.on('keyup', function(e) {
                if (e.which === 8 || e.which === 46) { // Backspace or Delete
                    var searchTerm = $(this).val().toLowerCase().trim();
                    if (searchTerm === '') {
                        clearTimeout(searchTimeout);
                        performSearch(''); // Clear search
                    }
                }
            });
        }

        function performSearch(searchTerm) {
            // Get the currently active tab
            var activeTab = $('.kt-tab-toggle.active').data('kt-tab-toggle');
            var currentTable;

            // Determine which table to search
            if (activeTab === '#tab_Active') {
                currentTable = $('#active_table');
            } else if (activeTab === '#tab_InActive') {
                currentTable = $('#inactive_table');
            } else if (activeTab === '#tab_All') {
                currentTable = $('#all_table');
            }

            if (!currentTable) return;

            // Perform the search
            var rows = currentTable.find('tbody tr');
            var visibleCount = 0;

            rows.each(function() {
                var row = $(this);
                var locationName = row.find('td:nth-child(2)').text().toLowerCase();
                var employeeCount = row.find('td:nth-child(3)').text().toLowerCase();
                var activeStatus = row.find('td:nth-child(4)').text().toLowerCase();
                var billableStatus = row.find('td:nth-child(5)').text().toLowerCase();

                // Check if search term matches any column
                var matches = locationName.includes(searchTerm) ||
                             employeeCount.includes(searchTerm) ||
                             activeStatus.includes(searchTerm) ||
                             billableStatus.includes(searchTerm);

                if (searchTerm === '' || matches) {
                    row.show();
                    visibleCount++;
                } else {
                    row.hide();
                }
            });

            // Update pagination info if needed
            updateSearchResults(visibleCount, rows.length);
        }

        function updateSearchResults(visibleCount, totalCount) {
            // Update the datatable info if it exists
            var activeTab = $('.kt-tab-toggle.active').data('kt-tab-toggle');
            var infoElement = $(activeTab).find('[data-kt-datatable-info="true"]');

            if (infoElement.length > 0) {
                if (visibleCount === totalCount) {
                    infoElement.text(`Showing ${totalCount} entries`);
                } else {
                    infoElement.text(`Showing ${visibleCount} of ${totalCount} entries (filtered)`);
                }
            }
        }

        // converts an array of items to a concatenated semicolumn joined string for example: [1, 2, 3] => "1;2;3"
        function ArrayToString(arr) {
            if (arr.length == 0) {
                return;
            }
            var r = "";
            for (var i = 0; i < arr.length; i++) {
                r = r + arr[i] + ";";
            }
            r = r.substring(0, r.length - 1);
            return r;
}
    });
    </script>
}
