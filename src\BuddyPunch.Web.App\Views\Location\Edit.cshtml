@using BuddyPunch.Web.App.Helpers
@model BuddyPunch.Web.App.ViewModel.LocationViewModel
@{
    ViewBag.Title = "Edit Location";
}

<div class="kt-card rounded-xl mb-7.5">
    <div class="p-7">
        <div class="flex flex-wrap justify-end items-center mb-6 gap-4">
            <div class="flex gap-2">
                <form method="post" action="@Url.Action("Clone")">
                    @Html.Hidden("id", Model.Id)
                    <button class="kt-btn kt-btn-mono cloneBtn" type="submit">Clone</button>
                </form>
                @if (Model.CanDelete)
                {
                    <form method="post" action="@Url.Action("Delete")">
                        @Html.Hidden("id", Model.Id)
                        <button id="deleteLocation" class="kt-btn kt-btn-danger" type="submit">Delete</button>
                    </form>
                }
            </div>
        </div>
        @Html.Partial("_ErrorSuccessPartial")
        @using (Html.BeginForm())
        {
            @Html.AntiForgeryToken()
            @Html.Partial("_ValidationSummary", ViewData.ModelState)
            @Html.HiddenFor(m => m.Id)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <div class="mb-5">
                        @Html.LabelFor(model => model.Name, new { @class = "block text-sm font-medium text-secondary-foreground mb-2" })
                        @Html.TextBoxFor(model => model.Name, new { @class = "kt-input w-full" })
                        @Html.ValidationMessageFor(model => model.Name, null, new { @class = "text-danger text-xs mt-1" })
                    </div>
                    <div class="mb-5">
                        <label class="block text-sm font-medium text-secondary-foreground mb-2">
                            Payroll Code
                            <a href="https://docs.buddypunch.com/en/articles/6728715-what-is-the-payroll-code-used-for-on-location-codes" target="_blank" title='Click for help article'>
                                <i class="ki-filled ki-question-circle text-base text-primary align-middle"></i>
                            </a>
                        </label>
                        @Html.TextBoxFor(model => model.PayrollCode, new { @class = "kt-input w-full" })
                        @Html.ValidationMessageFor(model => model.PayrollCode, null, new { @class = "text-danger text-xs mt-1" })
                    </div>
                    <div class="flex items-center gap-6 mb-5">
                        <label class="flex items-center gap-2">
                            <input id="Enabled" type="checkbox" name="Enabled" value="true" @(Model.Enabled ? "checked" : "") @(!Model.CanDisable ? "disabled" : "") class="kt-checkbox" />
                            <span class="text-sm">Active</span>
                            @if (!Model.CanDisable)
                            {
                                List<string> linkedEntities = new List<string>();
                                if (Model.DisableErrors.Any(x => x.Contains("geofence"))) { linkedEntities.Add("geofence(s)"); }
                                if (Model.DisableErrors.Any(x => x.Contains("shift"))) { linkedEntities.Add("current or future shift(s)"); }
                                <span class="italic text-xs text-secondary-foreground">(used by @(string.Join(" and ", linkedEntities)))</span>
                                <input id="Enabled" type="hidden" name="Enabled" value="true" />
                            }
                        </label>
                        <label class="flex items-center gap-2">
                            @Html.CheckBoxFor(model => model.Billable, new { @class = "kt-checkbox" })
                            <span class="text-sm">Billable</span>
                            <a href="https://docs.buddypunch.com/en/articles/5128286-what-is-the-billable-option-used-for-on-location-and-department-codes" target="_blank" title='Click for help article'>
                                <i class="ki-filled ki-question-circle text-base text-primary align-middle"></i>
                            </a>
                        </label>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6 pb-6">
                @if (Model.Employees.Any())
                {
                    <div>
                        <h5 class="text-base font-semibold mb-2">Select the employees assigned to this Location</h5>
                        <p class="text-xs text-secondary-foreground mb-2">An employee may be assigned multiple locations.</p>
                        <div class="kt-scrollable-x-auto">
                            <table class="kt-table table-auto kt-table-border w-full" id="employees_table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="kt-checkbox group-checkable" data-set="#employees_table .checkboxes" />
                                            <span class="text-xs ml-2">All</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var staff in Model.Employees)
                                    {
                                        var autoAssignedEntities = new List<string>();
                                        if (Model.LocationGeofenceStaffIds.Contains(staff.Id)) { autoAssignedEntities.Add("geofence(s)"); }
                                        if (Model.LocationShiftsStaffIds.Contains(staff.Id)) { autoAssignedEntities.Add("current or future shift(s)"); }
                                        <tr>
                                            <td>
                                                <label class="flex items-center gap-2">
                                                    <input class="kt-checkbox checkboxes" id="staff@(staff.Id)" type="checkbox" name="LocationStaffIds" value="@staff.Id" @(Model.LocationStaffIds.Contains(staff.Id) ? "checked" : "") @(autoAssignedEntities.Count > 0 ? "disabled" : "") />
                                                    <span class="text-sm">@staff.FullName</span>
                                                    @if (autoAssignedEntities.Count > 0)
                                                    {
                                                        <input id="staff@(staff.Id)" type="hidden" name="LocationStaffIds" value="@staff.Id" />
                                                        <span class="italic text-xs text-secondary-foreground">(used by @(string.Join(" and ", autoAssignedEntities)))</span>
                                                    }
                                                </label>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
                @if (Model.JobCodes.Any())
                {
                    <div>
                        <h5 class="text-base font-semibold mb-2">Department Codes for this location</h5>
                        <p class="text-xs text-secondary-foreground mb-2">A department may be assigned multiple locations.</p>
                        <div class="kt-scrollable-x-auto">
                            <table class="kt-table table-auto kt-table-border w-full" id="locations_table">
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="kt-checkbox group-checkable" data-set="#locations_table .checkboxes" />
                                            <span class="text-xs ml-2">All</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var jobCode in Model.JobCodes)
                                    {
                                        <tr>
                                            <td>
                                                <label class="flex items-center gap-2">
                                                    <input id="jobCode@(jobCode.Id)" class="kt-checkbox checkboxes" type="checkbox" name="LocationJobCodeIds" value="@jobCode.Id" @(Model.LocationJobCodeIds.Contains(jobCode.Id) ? "checked" : "") @(Model.LocationGeofenceJobCodeIds.Contains(jobCode.Id) ? "disabled" : "") />
                                                    <span class="text-sm">@jobCode.Name</span>
                                                    @if (Model.LocationGeofenceJobCodeIds.Contains(jobCode.Id))
                                                    {
                                                        <input id="jobCode@(jobCode.Id)" type="hidden" name="LocationJobCodeIds" value="@jobCode.Id" />
                                                        <span class="italic text-xs text-secondary-foreground">(used by a geofence)</span>
                                                    }
                                                    @if (!jobCode.Enabled)
                                                    {
                                                        <span class="kt-badge kt-badge--metal ml-2">Inactive</span>
                                                    }
                                                </label>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
            </div>
            <div class="flex items-center gap-3 mt-12">
                <input type="submit" value="Save" class="kt-btn kt-btn-primary" />
                @Html.ActionLink("Cancel", "Index", null, new { @class = "kt-btn kt-btn-mono" })
            </div>
        }
    </div>
</div>

<!-- Clone Confirmation Modal -->
<div class="kt-modal" id="cloneConfirmModal" style="display: none;">
    <div class="kt-modal-backdrop" onclick="closeCloneModal()"></div>
    <div class="kt-modal-content max-w-md">
        <div class="kt-modal-header">
            <h3 class="kt-modal-title">Clone this location?</h3>
            <button class="kt-modal-close" onclick="closeCloneModal()">
                <i class="ki-duotone ki-cross">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
            </button>
        </div>
        <div class="kt-modal-body">
            <p class="text-sm text-gray-600">A cloned location will be created with the same employees and department codes assigned.</p>
        </div>
        <div class="kt-modal-footer">
            <button class="kt-btn kt-btn-light" onclick="closeCloneModal()">Cancel</button>
            <button class="kt-btn kt-btn-primary" onclick="confirmClone()">Yes, I understand</button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="kt-modal" id="deleteConfirmModal" style="display: none;">
    <div class="kt-modal-backdrop" onclick="closeDeleteModal()"></div>
    <div class="kt-modal-content max-w-md">
        <div class="kt-modal-header">
            <h3 class="kt-modal-title">
                <i class="ki-duotone ki-warning text-warning me-2">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                Delete Location
            </h3>
            <button class="kt-modal-close" onclick="closeDeleteModal()">
                <i class="ki-duotone ki-cross">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
            </button>
        </div>
        <div class="kt-modal-body">
            <div class="text-center">
                <div class="mb-4">
                    <i class="ki-duotone ki-trash text-danger" style="font-size: 3rem;">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                        <span class="path5"></span>
                    </i>
                </div>
                <h4 class="mb-3 text-gray-900">Are you sure you want to delete this location?</h4>
                <p class="text-sm text-gray-600 mb-4">
                    This action cannot be undone. All data associated with this location will be permanently removed.
                </p>
                <div class="bg-light-warning p-3 rounded mb-4">
                    <p class="text-warning text-sm mb-0">
                        <i class="ki-duotone ki-information-5 me-1">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <strong>Warning:</strong> This will permanently delete the location and all associated data.
                    </p>
                </div>
            </div>
        </div>
        <div class="kt-modal-footer">
            <button class="kt-btn kt-btn-light" onclick="closeDeleteModal()">Cancel</button>
            <button class="kt-btn kt-btn-danger" onclick="confirmDelete()">
                <i class="ki-duotone ki-trash me-1">
                    <span class="path1"></span>
                    <span class="path2"></span>
                    <span class="path3"></span>
                    <span class="path4"></span>
                    <span class="path5"></span>
                </i>
                Yes, Delete Location
            </button>
        </div>
    </div>
</div>

@section PageHeadSection {
    <style>
        .kt-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1050;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .kt-modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .kt-modal-content {
            position: relative;
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow: auto;
        }

        .kt-modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .kt-modal-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin: 0;
        }

        .kt-modal-close {
            background: none;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            padding: 0.25rem;
            color: #6b7280;
        }

        .kt-modal-close:hover {
            color: #374151;
        }

        .kt-modal-body {
            padding: 1.5rem;
        }

        .kt-modal-footer {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
        }
    </style>
}
@section PageBeforeEndBodySection {
    <script>
        jQuery(document).ready(function () {
            $("#employees_table").setupSelectAllTable();
            $("#locations_table").setupSelectAllTable();
            $('.cloneBtn').click(function (event) {
                event.preventDefault();
                var form = $(event.target).parents('form');
                $('#cloneConfirmModal').show();
                $('#cloneConfirmModal').data('form', form);
            });
            $('#deleteLocation').click(function (event) {
                event.preventDefault();
                var form = $(event.target).parents('form');
                $('#deleteConfirmModal').show();
                $('#deleteConfirmModal').data('form', form);
            });
        });

        // Modal functions
        function closeCloneModal() {
            $('#cloneConfirmModal').hide();
            $('#cloneConfirmModal').removeData('form');
        }

        function confirmClone() {
            var form = $('#cloneConfirmModal').data('form');
            if (form) {
                form.submit();
            }
            closeCloneModal();
        }

        // Delete modal functions
        function closeDeleteModal() {
            $('#deleteConfirmModal').hide();
            $('#deleteConfirmModal').removeData('form');
        }

        function confirmDelete() {
            var form = $('#deleteConfirmModal').data('form');
            if (form) {
                form.submit();
            }
            closeDeleteModal();
        }
    </script>
}